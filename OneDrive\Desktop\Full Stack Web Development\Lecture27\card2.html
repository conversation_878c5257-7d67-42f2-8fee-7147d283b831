<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Second Card</title>
    <style>
        *{
            padding:0;
            margin:0;
            box-sizing: border-box;
        }
        .container{
            height:100vh;
            width:100vw;
            /* min-height: 1000px; */
            background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .box{
            height: 600px;
            width:400px;
            display: flex;
            flex-direction: column;
            margin: auto;
            background-color: white;
            border:1px solid black;
            border-radius: 20px;
            align-items: center;
            box-shadow: 1px 1px 10px black;
            gap:25px;
        }
        .box img{
            width: 100%;
            height:30%;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }
        .text-box{
            display: flex;
            flex-direction: column;
            align-items: center;
            gap:25px;
            width:75%;
            text-align:center;
        }
        #amount-box{
            width:80%;
            height: 10%;
            background-color: rgb(98, 174, 245);
            border-radius: 10px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding-right:20px;
            padding-left:20px;
        }
        #left-box{
            /* width:fit-content; */
            display: flex;
            gap:15px;
            height: 100%;
            flex-direction: row;
            align-items: center;
        }
        .img-box img{
            height:3.5rem;
            width:3.5rem;
        }
        .right-box{

        }
        #payment-but{
            width:80%;
            height: 9%;
            background-color: rgb(0, 0, 92);
            border-radius: 10px;
            color:white;
            cursor: pointer;
            box-shadow:0 10px 20px rgba(0,0,0,0.19),0 6px 6px rgba(0,0,0,0.23);
        }
        .cancel{
            width:80%;
            height: 10%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .cancel a{
            text-decoration: none;
            color: rgb(46, 43, 43);
            font-size: large;
            font-weight: bold;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="box">
            <img src="/Lecture 27/icon.jpg" alt="icon">
            <h2>Order Summary</h2>
            <div class="text-box">
                <p>Lorem ipsum dolor sit amet consectetur, adipisicing elit. Saepe, laboriosam? Adipisci neque fugit vel enim laudantium quos ipsum inventore delectus!</p>
            </div>
            <div id="amount-box">
                <div id="left-box">
                    <div class="img-box">
                        <img src="/Lecture 27/music.png" alt="">
                    </div>
                    <div class="amt-text-box">
                        <p>Annual Plan</p>
                        <p>$59.99/year</p>
                    </div>
                </div>
                <div class="right-box">
                    <a href="#">Change</a>
                </div>
            </div>
            <button id="payment-but">
                Proceed to Payment
            </button>
            <div class="cancel">
                <a href="">Cancel Order</a>
            </div>
        </div>
    </div>
</body>
</html>