*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
.container{
    height:100vh;
    width:100vw;
    overflow-x:none;
    background-color: #e2e5e8;
    background-image: linear-gradient(98deg,#e2e5e8,0%,#ffffff 100%);
}
.nav-container {
    width: 100vw;
    background-color: #0093E9;
    background-image: linear-gradient(160deg, #0093E9 0%, #80D0c7 100%);
    min-height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

nav{
    width:100%;
    overflow-x:none;
    padding-right: 2rem;
}
#nav-list{
    /* margin-left:15px;
    margin-right:15px; */
    list-style: none;
    font-size: 2.5rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width:100%;
    padding:15px;
}
#nav-logo{
    font-size: 2.8rem;
    color:white;
}

a{
    text-decoration: none;
    color:azure;
}
#nav-items{
    display: flex;
    gap:40px;
}

.footer-box{
    background-color: #0093E9;
    background-image: linear-gradient(160deg, #0093E9 0%, #80D0c7 100%);  
    width:100%; 
    text-align: center;
    min-height: 40px;

}

.footer-box p{
    color:azure;
    font-size:2rem;
    position:relative;
}

.content-box{
    width:70%;
    margin:auto;
    min-height: 80vh;
    /* border:1px solid black; */
    background-color: white;
    display: flex;
    flex-direction: column;
    gap: 100px;
    padding:3rem;
    box-shadow: 0px 0px 30px black;
}

.item{
    display: flex;
    justify-content: space-around;
    align-items: center;
}
.left-box{
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.left-box h2{
    font-size: 2rem;
}
.right-box{
    position: relative;
    overflow: hidden;
    transition: all 0.3s linear 0s;
    box-shadow: 1px 1px 10px black;
    border-radius: 20px;;
}
.right-box img{
    width:100%;
    height: auto;
    border-radius: 20px;
    transition: all 0.6s linear 0s;
}
.right-box:hover{
    transform:scale(1.1);
}
.ing-box{
    font-size:1.3rem;
}

.recipe-box{
    font-size: 1.1rem;
}
.right-box .overlay{
    position: absolute;
    top:0;
    left:0;
    height: 100%;
    width:100%;
    background-color: rgba(48, 45, 45, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    opacity:0;
}
.right-box:hover .overlay{
    opacity:1;
    transition: all 0.3s linear 0s;
}
.right-box .overlay span{
    color:white;
    font-size: 1.2rem;
}