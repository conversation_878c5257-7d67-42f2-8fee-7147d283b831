*{
    padding:0;
    margin:0;
    box-sizing:border-box;
}
body{
    height:100vh;
    width:100vw;
}
.container{
    height:100%;
    width:100%;
    background-color: rgb(59,57,57);
    display: grid;
    padding:4rem;
    row-gap:1rem;
    column-gap: 1rem;
    /* grid-template-rows: 100px 100px; */
    grid-template-rows: repeat(6,1fr);
    /* grid-template-columns: 100px 100px 100px; */
    grid-template-columns: repeat(1,1fr);
}
.item{
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(94,94,194);
    border: 5px solid black;
    color:white;
}
.header{
    grid-column-start: 1;
    grid-column-end: 4;
}
.sidebar{
    grid-row-start: 2;
    grid-row-end: 4;
    grid-column-start: 1;
    grid-column-end: 2;
}
.content1{
    grid-column-start: 2;
    grid-column-end: 4;
}
.footer{
    grid-column-start: 1;
    grid-column-end: 4;   
}

@media (min-width:768px)
{   
    .container{
        /* grid-template-rows: 100px 100px; */
        grid-template-rows: repeat(4,1fr);
        /* grid-template-columns: 100px 100px 100px; */
        grid-template-columns: repeat(3,1fr);
    }
    .header{
    grid-column-start: 1;
    grid-column-end: 4;
    }
    .sidebar{
    grid-row-start: 2;
    grid-row-end: 4;
    grid-column-start: 1;
    grid-column-end: 2;
    }
    .content1{
    grid-column-start: 2;
    grid-column-end: 4;
    }
    .footer{
    grid-column-start: 1;
    grid-column-end: 4;   
    }
}