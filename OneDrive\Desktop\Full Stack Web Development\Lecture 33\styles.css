@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;400;500;600;700&display=swap');

:root {
    --primary-color: #6366F1;
    --accent-color: #3f83f8;
    --text-color: #333333;
    --link-color: #2563eb;
    --background-color: #ffffff;
    --light-gray: #f0f0f0;
    --gray: #808080;
    --dark-gray: #555;
}

*{
    margin:0;
    padding:0;
    font-family: 'Inter', sans-serif;
    text-decoration: none;
}

body{
    height:100vh;
    text-align: center;
    background-color: var(--background-color);
    padding: 0.1rem;
}

.header-content{
    max-width: 1280px;
    margin:0 auto;
    display:flex;
    justify-content: space-between;
    align-items: center;
    padding:1rem;
}

.logo{
    display: flex;
    align-items: center;
    font-size:2rem;
    font-weight:bold;
    color:var(--text-color);
    text-decoration: none;
    gap:0.625rem
}

.logo-icon{
    height:5.875;
    width:1.938rem;
}

.logo-text{
    font-size: 2rem;
    font-weight: bold; 
}

.nav{
    display: none;
    gap:1.5rem;
}

@media screen and (min-width:768px) {
    .nav{
        display: flex;
    }
} 

.nav-link{
    color:#718096;
    font-size: 1.25rem;
    font-weight: 600;
    transform: color 0.1s;
}

.nav-link:hover{
    color:var(--link-color)
}

.contact-button{
    display: none;
    border:none;
    border-radius: 0.375rem;
    background-color: var(--primary-color);
    color:var(--background-color);
    transform: all 1s;
    padding:0.75rem 1.5rem;
    font-size:1rem;
    font-weight: 600;
}

@media screen and (min-width:840px)
{
    .contact-button{
        display:block;
    }
}

.contact-button:hover{
    background-color: #5254f8;
}

.menu-button{
    display:none;
    border:none;
    border-radius: 0.375rem;
    background-color:var(--background-color);
    font-size:1rem;
    padding:0.5rem 0.9rem;
    font-weight: 600;
}

@media screen and (max-width:768px){
    .menu-button{
        display: block;
    }
}

.menu-icon{
    height: 1.5rem;
    width: 1.5rem;
}

.main-section{
    max-width: 1280px;
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 0.6rem;
    align-items: center;
    margin:0 auto;
}

.content-left{
    text-align: center;
    animation: slideFromLeft 1s ease forwards;
    opacity:0;
}

@keyframes slideFromLeft{
    0%{
        opacity:0;
        transform: translateX(-100%);
    }
    100%{
        opacity:1;
        transform: translateX(0);
    }
}

.section-label{
    font-size:1.15rem;
    font-weight:600;
    color:var(--primary-color); 
}

.section-title{
    font-size:2.3rem;
    color:var(--text-color);
    padding:1.1rem;
}

.section-desc{
    color:var(--gray);
    font-size:1.13rem;
}
.button-group{
    display: flex;
    gap:2rem;
    padding:1rem;
    margin-top: 1rem;
    align-items: center;
    justify-content: center;
}
.start-button,.tour-button{
    /* margin-top: 2rem; */
    border:none;
    border-radius: 0.375rem;
    background-color: var(--primary-color);
    color:var(--background-color);
    transform: all 1s;
    padding:0.75rem 1.5rem;
    font-size:1rem;
    font-weight: 600;
}

.tour-button{
    background-color: var(--dark-gray);
}

.start-button:hover{
    background-color: #5254f8;
}

.tour-button:hover{
    background-color: var(--gray);
} 

.content-right{
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-container{
    border:none;
    border-radius:1.225rem;
    overflow:hidden;
    box-shadow:0px 4px 8px rgba(0,0,0,0.1);
    height: 50vh;
}

.section-image{
    width:100%;
    height:100%;
    /* object-fit:cover;
    object-position:center; */
}

@media screen and (min-width:768px)
{
    .main-section{
        flex-direction: row;
    }
    .content-left{
        max-width:50%;
        text-align: left;
    }
    .section-title{
        padding-left: 0;
        font-size: 3.8rem;
    }
    .button-group{
        justify-content: start;
        padding-left:0;
    }
    .section-label{
        font-size: 1rem;
    }
    .image-container{
        height:65vh;
    }
    .section-desc{
        font-size:1.1rem;
        color:var(--gray);
        width:78%;
    }
}

.company-container{
    display:flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top:2rem;   
    animation: slideFromLeft 1s ease forwards; 
}

.company-grid{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    gap:3.6rem;
    padding:1rem;
    background-color: var(--light-gray);
    border:none;
    border-radius:0.625rem;
    margin-top:1.5rem;
    margin-bottom:2rem;
    padding:1.8rem 2.5rem;
}

@media screen and (min-width:640px)
{
    .company-grid{
        grid-template-columns: repeat(4,1fr);
    }
    .company-title{
        font-size:2.3rem;
        font-weight:bold;
    }
    .company-container{
        margin-bottom: 0rem;
    }
}

.company-logo{
    font-size:1rem;
    font-weight:500;
    color:var(--gray); 
    display:flex;
    gap:0.5rem;
}

.svg-img{
    height: 1.56rem;
}

.company-grid .logo-text{
    font-size: 1rem;
}

.feature-container{
    background-color: var(--light-gray);
    padding:2.5rem 0;
    text-align: center;
}

.feature-content{
    max-width: 800px;
    margin:0 auto;
    padding:0 2rem;

}

.main-info{
    display: flex;
    flex-direction: column;
    gap:1rem;
}
.main-title{
    font-size: 2.1rem;
    font-weight: bold;
}

.main-desc{
    color:var(--gray);
    font-size: 1rem;
}

.feature-grid{
    display:grid;
    grid-template-columns: repeat(2,1fr);
    grid-gap:2rem;
    margin-top:1.3rem;
}

.feature-info{
    display: flex;
    flex-direction: column;
    gap:1rem;
}

.icon-container{
    background-color: var(--background-color);
    height:3rem;
    width:3rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.feature-svg{
    width:1.5rem;
    height: 1.5rem;
}

.feature-card{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: #2563eb;  
    padding:1.6rem;
    border-radius: 1rem;
    gap: 1rem;
}

.feature-title{
    color:white;
    font-weight:bold;
    font-size: 1.3rem;
}

.feature-card:nth-child(1)
{
    background-color: #4a90e2;
}
.feature-description{
    color:white;
}

.feature-card:nth-child(2)
{
    background-color: #ff6289;
}

.feature-card:nth-child(3)
{
    background-color: #fcbf58;
}

.feature-card:nth-child(4)
{
    background-color: #44bfc3;
}

.feature-card:nth-child(5)
{
    background-color:rgb(119, 176, 93);;
}

.feature-card:nth-child(6)
{
    background-color: #7d78b1;
}

@media screen and (max-width:768px)
{
    .feature-grid{
        grid-template-columns: repeat(1,1fr);
    }
}
.testimonial-container{
    background-color: var(--background-color);
    padding: 2rem;
    margin: 0 auto;
    max-width: 1280px;
    width: 100%;
}
.testimonial-content{
    display: flex;
    flex-direction: column;
    gap: 2rem;
}
.testimonial-title{
    font-size: 1.875rem;
    font-weight: bold;
}

.testimonial-grid{
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    grid-gap: 2rem;
}

.testimonial-card{
    display: flex;
    flex-direction: column;
    border: 1px solid rgb(171, 163, 163);
    border-radius: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1.2rem;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover{
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    border: 1px solid blue;
}

.testimonial-avatar img{
    height: 5rem;
    width: 5rem;
}

.testimonial-text{
    color: #555;
    font-size: 0.875rem;
}

.testimonial-name{
    color: var(--primary-color);
    font-size: 1rem;
}

.testimonial-desc{
    font-size: 0.875rem;
    color: #777;
}

.newsletter-container{
    background-color: var(--background-color);
    max-width: 1280px;
    height:40vh;
    margin:0 auto;
}

.newsletter-content{
    background-color: var(--light-gray);
    display: flex;
    flex-direction: row;
    height:100%;
    border-radius: 8px;
    overflow:hidden;
}

.news-left{
    width:50%;
    height:100%;
    display: none;
    position:relative;
}

.news-left img{
    position: absolute;
    inset:0;
    width:100%;
    height:100%;
    object-fit:cover;
    object-position:center;
}

.news-right{
    display: flex;
    flex-direction: column;
    gap:1rem;
    padding:0.5rem;
    justify-content: center;
    align-items: flex-start;
    width:98%;
}

.news-title{
    font-size:2.5rem;
    font-weight:bold;
    color:var(--primary-color);
    
}

@media screen and (min-width:640px) {
    .news-left{
        display: block;
        width: 50%;
    }
    .news-right{
        width: 50%;
    }
    .newsletter-container{
        padding: 2rem;
    }
}

.news-info{
    padding:0 3rem;
}

.news-desc{
    text-align:left;
    color:#777;
}

.news-form{
    padding: 0 3rem;
    display: flex;
    flex-wrap: wrap;
    margin-bottom:1rem;
    gap:0.6rem;
}

.news-email{
   background-color: var(--background-color); 
   /* width:100%; */
   color:#333;
   outline:none;
   border:1px solid #ccc;
   border-radius:4px;
   padding:0.5rem 0.75rem;; 
}

.news-email:focus{
    border-color:#2563eb;
    box-shadow:0 4px 8px rgba(0,0,0,0.3);
}

.news-send-button{
    background-color: var(--primary-color);
    color:var(--background-color);
    border:none;
    border-radius: 0.25rem;
    padding:0.5rem 1rem;
}