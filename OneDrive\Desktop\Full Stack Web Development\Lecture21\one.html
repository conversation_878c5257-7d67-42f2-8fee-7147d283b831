<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .container{
            background-color: beige;
            border:1px solid black;
            margin:2px;
            padding:2px;
            display: flex;
        }
        .box{
            height:200px;
            width:200px;
            border:2px solid brown;
            margin:2px;
            padding:2px;

        }
        #box1{
            background-color: aqua;
        }
        #box2{
            background-color: blue;
        }
        #box3{
            background-color: orange;
        }
        #box4{
            background-color: brown;
        }

        @media(min-width:640px){
            .container{
                flex-direction: row;
            }
        }
        @media(max-width:640px){
            .container{
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="box" id="box1">Box1</div>
        <div class="box" id="box2">Box2</div>
        <div class="box" id="box3">Box3</div>
        <div class="box" id="box4">Box4</div>
    </div>
</body>
</html>