*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Times New Roman', Times, serif;
}

body{
    background-color: rgb(89, 90, 106);
    color:white;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container{
    display: flex;
    flex-direction: column;
    gap: 1rem;
    background-color: rgb(69, 64, 64);
    padding: 20px;
    border-radius: 5px;
    width: 50%;
    max-width: 600px;
}

h1{
    font-size: 2rem;
}

.user-container{
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: start;
}

.user-input-container{
    display: flex;
    justify-content: space-between;
}

#user-input{
    width: 80%;
    padding: 0.4rem;
    border-radius: 5px;
}

#search-btn{
    padding: 0.4rem;
    border-radius: 5px;
}

.circle{
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid #299f5d;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1 rem;
    color: white;
    font-weight: 700;
    background: conic-gradient(#299f5d var(--progress-degree, 0%), #283a2e 0%);
    flex-direction: column;
}
.circle span{
    position: relative;
    z-index: 2;
}

.progress{
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    flex-wrap: wrap;
    gap: 10px;
}

.stats-cards{
    margin-top: 2rem;
    margin-bottom: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: space-evenly;
}

h4{
    font-size: 1rem;
}

.card{
    background-color: rgb(230, 146, 44);
    width: 40%;
    max-width: 290px;
    padding: 10px;
    border-radius: 10px;
    font-size: 1rem;
    min-height: 4rem;
}