*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family:'Times New Roman', Times, serif;
}

body{
    background-color: rgb(89, 90, 106);
    color:white;
    height:100vh;
    display:flex;
    justify-content: center;
    align-items: center;
}

.container{
    display:flex;
    flex-direction:column;
    gap:1.5rem;
    background-color: rgb(69, 64, 64)  ;
    padding:20px;
    border-radius:5px;
    width:50%;
    max-width:600px;
}

h1{
    font-size:2rem;
}

.user-container{
    display:flex;
    flex-direction:column;
    gap:10px;
    justify-content:start;
}

.user-input-container{
    display:flex;
    justify-content:space-between;
}

#user-input{
    width:80%;
    padding:0.4rem;
    border-radius: 5px;
}

#search-button{
    padding:0.4rem;
    border-radius: 5px;
}

.circle{
    width:120px;
    height:120px;
    border-radius: 50%;
    border:4px solid #299f5d;
    position:relative;
    display:flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size:16px;
    color:white;
    font-weight:700;
    background: conic-gradient(#299f5d var(--progress-degree, 0%),#283a2e 0%);
}

.circle span{
    position: relative;
    z-index:2;
}

.progress{
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    flex-wrap:wrap;
    gap:10px;
}