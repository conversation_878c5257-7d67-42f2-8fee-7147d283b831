*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body{
    height: 100%;
    width: 100%;
}

a{
    text-decoration: none;
}

.container{
    max-width: 66vw;
    padding: 2rem 1rem;
    margin: 0 auto;
    /* background-color: aqua; */
}

.header_container{
    margin-bottom: 2.8rem;
}

.header_title{
    margin-bottom: 1rem;
    font-size: 1.7rem;
    line-height: 2rem;
    font-weight: 700;
    text-align: center;
}

.header_desc{
    max-width: 568px;
    text-align: center;
    color: rgb(105, 99, 99);
    margin: 0 auto;
}
.main_container{
    display: grid;
    gap: 1.5rem;
    /* grid-template-columns: repeat(1, 1fr); */
    grid-template-columns: repeat(auto-fit, minmax(350px, 400px));
    justify-content: center;
}

/* @media (min-width:935px) {
    .main_container{
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width:1380px) {
    .main_container{
        grid-template-columns: repeat(3, 1fr);
    }
}
@media (min-width:1870px) {
    .main_container{
        grid-template-columns: repeat(4, 1fr);
    }
} */

.card_container{
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgb(233, 224, 224);
    border-radius: 8px;
}

.card_image_container{
    position: relative;
    overflow: hidden;
    height: 7rem;
}

@media (min-width: 768px) {
    .card_image_container{
            height: 12rem;
    }
}

.card_image{
    object-fit: cover;
    object-position: center;
    position: absolute;
    height: 100%;
    width: 100%;
}

.card_title_container{
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
}

.card_container:hover .card_image{
    transform: scale(1.1);
    transition-duration: 200ms;
}

.card_title{
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5rem;
    margin-bottom: 0.3rem;
    color: rgb(25, 23, 23);
}


.card_title_anchor:hover .card_title{
    color:rgb(67, 67, 191);
}


.card_desc{
    color: rgb(113, 107, 107);
    font-size: 1rem;
}

.card_footer_container{
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 0 1.5rem;
    padding-bottom: 1.5rem;
}

.author_container{
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 1rem;
}

.author_avatar_container{
    height: 2.5rem;
    width: 2.5rem;
    overflow: hidden;
    border: 1px solid rgb(150, 147, 147);
    border-radius: 50%;
    background-color: rgb(243, 239, 239);
}

.author_info_container{
    display: flex;
    flex-direction: column;
}

.author_name{
    color: rgb(38, 38, 149);
}

.author_date{
    color: rgb(136, 126, 126);
}

.card_tag_container{
    font-size: 0.79rem;
    border: 1px solid rgb(76, 71, 71);
    border-radius: 4px;
    line-height: 1.3rem;
    padding: 0.25rem 0.5rem;
    color: grey;
}

