<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation</title>
    <style>
        *{
            margin: 0;
            padding: 0;
        }
        .container{
            width:100vw;
            height:100vh;
            background-color:beige;
            border:5px solid black;
            box-sizing: border-box;
        }
        .box{
            background-color: blue;
            width:350px;
            height:80px;
            border:3px solid yellow;
            border-radius: 10px;
            margin:10px;
            position:relative;
            /* animation-name: rightMovement;
            animation-duration: 5s;
            animation-iteration-count: infinite;
            animation-delay: 2s;
            animation-timing-function: linear;
            animation-timing-function: ease-in; 
            animation-timing-function: ease-out; 
            animation-timing-function:cubic-bezier(,0.7,0.1,0.6); 
            animation-direction: alternate-reverse; 
            animation-fill-mode: backwards;*/
            animation:5s linear 1s infinite alternate none running rightMovement;
        }
        @keyframes rightMovement{
            from{
                top:0;
                left:0;
            }
            to{
                top:0;
                left: 1400px;
            }
        }
        #box2{
            background-color: blue;
            width:350px;
            height:80px;
            border:3px solid yellow;
            border-radius: 10px;
            margin:10px;
            position:relative;
            animation:5s linear 1s infinite alternate none running leftMovement;
        }
        @keyframes leftMovement{
            from{
                top: 650px;;
                left: 1400px;;
            }
            to{
                top:650px;
                left: 0px;
            }
        }

        #circle{
            background-color: blue;
            width:100px;
            height:100px;
            border:3px solid yellow;
            border-radius: 50px;
            margin:10px;
            position:relative;
            top: 30%;
            left:40%;
            animation:10s ease-in-out 0s infinite alternate-reverse none running circleMovement;
        }
        @keyframes circleMovement{
            0%{
                top:1%;
                left:5%;
            }
            30%{
                top:5%;
                left:90%;
            }
            60%{
                top:70%;
                left:10%;
            }
            100%{
                top:75%;
                left:40%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="box">

        </div>
        <div id="circle"></div>
        <div id="box2"></div>
    </div>

</body>
</html>