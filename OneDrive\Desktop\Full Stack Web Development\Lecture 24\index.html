<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transition</title>
    <style>
        .box{
            background-color: beige;
            width:400px;
            height:400px;
            border:2px solid #000000;
            /* transition-property: all;
            transition-duration: 1s;
            transition-delay: 0s;
            transition-timing-function: ease-in-out; */
            /* shorthand notation */
            /* transition: all 1s ease-in-out 0s; */
            transition: width 5s,height 3s;
        }
        .box:hover{
            width:800px;
            height:1800px;
            background-color:blue;
        }
    </style>
</head>
<body>
    <div class="box">

    </div>
</body>
</html>