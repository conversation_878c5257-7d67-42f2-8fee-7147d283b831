.box{
    width:400px;
    height:400px;
    background-color:beige;
    border: 2px solid black;
    display:flex;
    justify-content: center;
    align-items:center;
    margin:auto;
    transition: all 2s linear 1s;
}
.container{
    height:100vh;
    width:100vh;
    display: flex;
    justify-content: center;
    align-items: center;   
}
.box:hover{
    /* transform: rotate(45deg); */
    /* transform: scale(2); */
    /* transform: rotate(-45deg); */
    /* transform: skew(45deg); */
    transform: translate(500px,500px );
}
p{
    font-size:36px ;
}