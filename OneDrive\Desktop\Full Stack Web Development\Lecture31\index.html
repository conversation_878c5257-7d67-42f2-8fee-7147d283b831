<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Grid Design</title>
    <style>
        *{
            margin:0;
            padding:0;
            box-sizing: border-box; 
        }
        body{
            width:100%;
            height:100%;
        }
        .container{
            width: 100%;
            height:100%;
            padding:4rem;
            display:grid;
            gap:1rem;
            background-color: rgb(71,65,65);
            grid-template-columns: repeat(auto-fit,minmax(200px,500px));
        }
       
        .item{
            border:3px solid black;
            background-color:rgb(85,85,208);
            color:white;
            padding:1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="item">A</div>
        <div class="item">B</div>
        <div class="item">C</div>
        <div class="item">D</div>
        <div class="item">E</div>
        <div class="item">F</div>
        <div class="item">G</div>
        <div class="item">H</div>
        <div class="item">I</div>
        <div class="item">J</div>
        <div class="item">K</div>
    </div>
</body>
</html>