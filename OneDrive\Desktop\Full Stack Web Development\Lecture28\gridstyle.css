*{
    padding:0;
    margin:0;
    box-sizing:border-box;
}
body{
    height:100%;
    width:100%;
}
.container{
    height: 100%;
    width:100%;
    background-color:rgb(69,62,62) ;
    display:grid;
    grid-template-columns: repeat(3,1fr);
    grid-template-rows: repeat(4,1fr);
    gap:0.4rem;
    padding:4rem;
    border:2px solid white;
    /* justify-items: end; */
    /* align-items: center; */
    place-items: center;
}
.item{
    height:100px;
    width:100px;
    background-color: rgb(26, 240, 90);
    border:1px solid rgb(217, 202, 202);
    /* justify-self: stretch; */
    /* align-self: center; */
    /* place-self: center; */
}